<?php $__env->startSection('title', 'Groups Management'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Groups Management</h3>
                    <a href="<?php echo e(route('admin.groups.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Group
                    </a>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-control" id="divisionFilter">
                                <option value="">All Divisions</option>
                                <?php $__currentLoopData = $divisions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $division): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($division->id); ?>"><?php echo e($division->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search groups...">
                        </div>
                    </div>

                    <?php if($groups->count() > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="groupsTable">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Division</th>
                                        <th>Category</th>
                                        <th>Products</th>
                                        <th>Status</th>
                                        <th>Sort Order</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <?php if($group->icon): ?>
                                                    <i class="<?php echo e($group->icon); ?>" style="color: <?php echo e($group->color ?? '#007bff'); ?>"></i>
                                                <?php endif; ?>
                                                <?php echo e($group->name); ?>

                                            </td>
                                            <td><?php echo e($group->division->name); ?></td>
                                            <td><?php echo e($group->division->category->name); ?></td>
                                            <td>
                                                <span class="badge badge-info"><?php echo e($group->products->count()); ?></span>
                                            </td>
                                            <td>
                                                <?php if($group->is_active): ?>
                                                    <span class="badge badge-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge badge-danger">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($group->sort_order ?? 0); ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('admin.groups.show', $group)); ?>" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="<?php echo e(route('admin.groups.edit', $group)); ?>" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="<?php echo e(route('admin.groups.destroy', $group)); ?>" method="POST" class="d-inline">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                                onclick="return confirm('Are you sure you want to delete this group?')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            <?php echo e($groups->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Groups Found</h5>
                            <p class="text-muted">Start by creating your first group.</p>
                            <a href="<?php echo e(route('admin.groups.create')); ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create First Group
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#groupsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

    // Division filter
    $('#divisionFilter').on('change', function() {
        var divisionId = $(this).val();
        $('#groupsTable tbody tr').each(function() {
            if (divisionId === '' || $(this).data('division-id') == divisionId) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Status filter
    $('#statusFilter').on('change', function() {
        var status = $(this).val();
        $('#groupsTable tbody tr').each(function() {
            if (status === '' || $(this).data('status') == status) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\erp-pos-system\resources\views/admin/groups/index.blade.php ENDPATH**/ ?>