<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

class BrandUser extends Pivot
{
    protected $table = 'brand_user';

    protected $fillable = [
        'user_id',
        'brand_id',
    ];

    public $timestamps = true;

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function brand()
    {
        return $this->belongsTo(Brand::class);
    }
}
