<?php $__env->startSection('title', 'Brands Management - ERP System'); ?>

<?php $__env->startSection('page-title', 'Brands Management'); ?>

<?php $__env->startSection('breadcrumb', 'Home > Admin > Brands'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .filters {
        background: #fff;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 25px;
    }
    .filters select, .filters input {
        padding: 12px 16px;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        margin-right: 15px;
        font-size: 14px;
        transition: all 0.3s ease;
    }
    .filters select:focus, .filters input:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }
    .table-container {
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        overflow: hidden;
    }
    table { width: 100%; border-collapse: collapse; }
    th, td { padding: 15px; text-align: left; border-bottom: 1px solid #f1f3f4; }
    th {
        background: #f8f9fa;
        font-weight: 600;
        color: #2c3e50;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    tr:hover { background: #f8f9fa; }
    .actions { display: flex; gap: 8px; }
    .company-badge {
        background: linear-gradient(135deg, #e9ecef, #f8f9fa);
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        color: #495057;
        font-weight: 500;
    }
    .header-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 25px;
        align-items: center;
    }
    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        text-align: center;
        transition: transform 0.3s ease;
    }
    .stat-card:hover {
        transform: translateY(-5px);
    }
    .stat-number {
        font-size: 28px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
    }
    .stat-label {
        font-size: 13px;
        color: #7f8c8d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="header-actions">
    <a href="<?php echo e(route('admin.brands.create')); ?>" class="btn btn-primary">
        <i style="margin-right: 8px;">➕</i> Add New Brand
    </a>
</div>

        <!-- Filters -->
        <div class="filters">
            <form method="GET" style="display: flex; align-items: center; gap: 15px;">
                <div>
                    <label for="company_id">Filter by Company:</label>
                    <select name="company_id" id="company_id" onchange="this.form.submit()">
                        <option value="">All Companies</option>
                        <?php $__currentLoopData = $companies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($company->id); ?>" <?php echo e(request('company_id') == $company->id ? 'selected' : ''); ?>>
                                <?php echo e($company->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div>
                    <input type="text" name="search" placeholder="Search brands..." value="<?php echo e(request('search')); ?>" 
                           style="width: 200px;" onkeyup="if(event.key==='Enter') this.form.submit()">
                </div>
                <button type="submit" class="btn btn-primary" style="padding: 8px 16px;">Search</button>
                <?php if(request()->hasAny(['company_id', 'search'])): ?>
                    <a href="<?php echo e(route('admin.brands.index')); ?>" class="btn btn-secondary" style="padding: 8px 16px;">Clear</a>
                <?php endif; ?>
            </form>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Brand Name</th>
                        <th>Company</th>
                        <th>Categories</th>
                        <th>Branches</th>
                        <th>Products</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($brand->id); ?></td>
                            <td><strong><?php echo e($brand->name); ?></strong></td>
                            <td>
                                <span class="company-badge"><?php echo e($brand->company->name); ?></span>
                            </td>
                            <td><?php echo e($brand->categories->count()); ?></td>
                            <td><?php echo e($brand->branches->count()); ?></td>
                            <td>
                                <?php
                                    $productCount = $brand->categories->sum(function($category) {
                                        return $category->divisions->sum(function($division) {
                                            return $division->groups->sum(function($group) {
                                                return $group->products->count();
                                            });
                                        });
                                    });
                                ?>
                                <?php echo e($productCount); ?>

                            </td>
                            <td><?php echo e($brand->created_at->format('M d, Y')); ?></td>
                            <td>
                                <div class="actions">
                                    <a href="<?php echo e(route('admin.brands.show', $brand)); ?>" class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">View</a>
                                    <a href="<?php echo e(route('admin.brands.edit', $brand)); ?>" class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">Edit</a>
                                    <form method="POST" action="<?php echo e(route('admin.brands.destroy', $brand)); ?>" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this brand? This will delete all related categories and products.')">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="submit" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                                <?php if(request()->hasAny(['company_id', 'search'])): ?>
                                    No brands found matching your criteria. <a href="<?php echo e(route('admin.brands.index')); ?>">View all brands</a>
                                <?php else: ?>
                                    No brands found. <a href="<?php echo e(route('admin.brands.create')); ?>">Add the first brand</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>

<?php if($brands->hasPages()): ?>
    <div style="margin-top: 30px; text-align: center;">
        <?php echo e($brands->appends(request()->query())->links()); ?>

    </div>
<?php endif; ?>

<!-- Quick Stats -->
<div class="stats-cards" style="margin-top: 30px;">
    <div class="stat-card">
        <div class="stat-number"><?php echo e($brands->total()); ?></div>
        <div class="stat-label">Total Brands</div>
    </div>
    <div class="stat-card">
        <div class="stat-number"><?php echo e($companies->count()); ?></div>
        <div class="stat-label">Companies</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">
            <?php echo e($brands->sum(function($brand) { return $brand->categories->count(); })); ?>

        </div>
        <div class="stat-label">Total Categories</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">
            <?php echo e($brands->sum(function($brand) { return $brand->branches->count(); })); ?>

        </div>
        <div class="stat-label">Total Branches</div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\erp-pos-system\resources\views/admin/brands/index.blade.php ENDPATH**/ ?>