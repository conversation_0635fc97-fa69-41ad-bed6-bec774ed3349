@extends('layouts.admin')

@section('title', 'Settings')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Application Settings</h3>
                </div>

                <div class="card-body">
                    <!-- Theme Settings -->
                    <div class="settings-section">
                        <h5 class="mb-3">
                            <i class="fas fa-palette"></i> Theme Settings
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="theme-option" data-theme="light">
                                    <div class="theme-preview light-theme {{ $currentTheme === 'light' ? 'active' : '' }}">
                                        <div class="theme-header"></div>
                                        <div class="theme-sidebar"></div>
                                        <div class="theme-content"></div>
                                    </div>
                                    <div class="theme-info">
                                        <h6>Light Theme</h6>
                                        <p class="text-muted">Clean and bright interface</p>
                                        <button class="btn btn-outline-primary theme-btn" data-theme="light">
                                            {{ $currentTheme === 'light' ? 'Current' : 'Select' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="theme-option" data-theme="dark">
                                    <div class="theme-preview dark-theme {{ $currentTheme === 'dark' ? 'active' : '' }}">
                                        <div class="theme-header"></div>
                                        <div class="theme-sidebar"></div>
                                        <div class="theme-content"></div>
                                    </div>
                                    <div class="theme-info">
                                        <h6>Dark Theme</h6>
                                        <p class="text-muted">Easy on the eyes for long sessions</p>
                                        <button class="btn btn-outline-primary theme-btn" data-theme="dark">
                                            {{ $currentTheme === 'dark' ? 'Current' : 'Select' }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- User Preferences -->
                    <div class="settings-section">
                        <h5 class="mb-3">
                            <i class="fas fa-cog"></i> User Preferences
                        </h5>

                        <form action="{{ route('admin.settings.update-preferences') }}" method="POST">
                            @csrf
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" name="sidebar_collapsed" id="sidebar_collapsed" 
                                                   class="form-check-input" value="1" 
                                                   {{ Session::get('preference_sidebar_collapsed', false) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="sidebar_collapsed">
                                                Collapse Sidebar by Default
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" name="notifications_enabled" id="notifications_enabled" 
                                                   class="form-check-input" value="1" 
                                                   {{ Session::get('preference_notifications_enabled', true) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="notifications_enabled">
                                                Enable Notifications
                                            </label>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="form-check">
                                            <input type="checkbox" name="auto_refresh" id="auto_refresh" 
                                                   class="form-check-input" value="1" 
                                                   {{ Session::get('preference_auto_refresh', false) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="auto_refresh">
                                                Auto-refresh Dashboard
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="items_per_page">Items per Page</label>
                                        <select name="items_per_page" id="items_per_page" class="form-control">
                                            <option value="10" {{ Session::get('preference_items_per_page', 20) == 10 ? 'selected' : '' }}>10</option>
                                            <option value="20" {{ Session::get('preference_items_per_page', 20) == 20 ? 'selected' : '' }}>20</option>
                                            <option value="50" {{ Session::get('preference_items_per_page', 20) == 50 ? 'selected' : '' }}>50</option>
                                            <option value="100" {{ Session::get('preference_items_per_page', 20) == 100 ? 'selected' : '' }}>100</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Preferences
                                </button>
                                <a href="{{ route('admin.settings.reset') }}" class="btn btn-outline-secondary"
                                   onclick="return confirm('Are you sure you want to reset all settings to default?')">
                                    <i class="fas fa-undo"></i> Reset to Default
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Info -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">About Settings</h5>
                </div>
                <div class="card-body">
                    <div class="info-item">
                        <h6><i class="fas fa-palette"></i> Theme</h6>
                        <p class="text-muted">Choose between light and dark themes. Your preference is saved and will persist across sessions.</p>
                    </div>

                    <div class="info-item">
                        <h6><i class="fas fa-bell"></i> Notifications</h6>
                        <p class="text-muted">Control whether you receive system notifications for important events.</p>
                    </div>

                    <div class="info-item">
                        <h6><i class="fas fa-sync"></i> Auto-refresh</h6>
                        <p class="text-muted">Automatically refresh dashboard data every 30 seconds to keep information current.</p>
                    </div>

                    <div class="info-item">
                        <h6><i class="fas fa-list"></i> Items per Page</h6>
                        <p class="text-muted">Set how many items to display in tables and lists throughout the application.</p>
                    </div>
                </div>
            </div>

            <!-- Current Settings Summary -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title">Current Settings</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><strong>Theme:</strong> {{ ucfirst($currentTheme) }}</li>
                        <li><strong>Sidebar:</strong> {{ Session::get('preference_sidebar_collapsed', false) ? 'Collapsed' : 'Expanded' }}</li>
                        <li><strong>Notifications:</strong> {{ Session::get('preference_notifications_enabled', true) ? 'Enabled' : 'Disabled' }}</li>
                        <li><strong>Auto-refresh:</strong> {{ Session::get('preference_auto_refresh', false) ? 'Enabled' : 'Disabled' }}</li>
                        <li><strong>Items per page:</strong> {{ Session::get('preference_items_per_page', 20) }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
.settings-section {
    margin-bottom: 2rem;
}

.theme-option {
    text-align: center;
    margin-bottom: 2rem;
}

.theme-preview {
    width: 100%;
    height: 120px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.theme-preview.active {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.theme-preview:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.light-theme {
    background: #ffffff;
}

.light-theme .theme-header {
    height: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.light-theme .theme-sidebar {
    position: absolute;
    left: 0;
    top: 20px;
    width: 30%;
    height: calc(100% - 20px);
    background: #343a40;
}

.light-theme .theme-content {
    position: absolute;
    right: 0;
    top: 20px;
    width: 70%;
    height: calc(100% - 20px);
    background: #ffffff;
}

.dark-theme {
    background: #2d3748;
}

.dark-theme .theme-header {
    height: 20px;
    background: #1a202c;
    border-bottom: 1px solid #4a5568;
}

.dark-theme .theme-sidebar {
    position: absolute;
    left: 0;
    top: 20px;
    width: 30%;
    height: calc(100% - 20px);
    background: #1a202c;
}

.dark-theme .theme-content {
    position: absolute;
    right: 0;
    top: 20px;
    width: 70%;
    height: calc(100% - 20px);
    background: #2d3748;
}

.theme-info h6 {
    margin-bottom: 0.5rem;
}

.info-item {
    margin-bottom: 1.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}
</style>
@endpush

@push('scripts')
<script>
$(document).ready(function() {
    // Theme selection
    $('.theme-btn').on('click', function() {
        const theme = $(this).data('theme');
        
        $.ajax({
            url: '{{ route("admin.settings.update-theme") }}',
            method: 'POST',
            data: {
                theme: theme,
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    // Update UI
                    $('.theme-preview').removeClass('active');
                    $('.theme-preview.' + theme + '-theme').addClass('active');
                    
                    $('.theme-btn').text('Select');
                    $('[data-theme="' + theme + '"] .theme-btn').text('Current');
                    
                    // Apply theme immediately
                    $('body').removeClass('theme-light theme-dark').addClass('theme-' + theme);
                    
                    // Show success message
                    toastr.success('Theme updated successfully!');
                    
                    // Reload page to apply theme fully
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            },
            error: function() {
                toastr.error('Failed to update theme');
            }
        });
    });
});
</script>
@endpush
@endsection
