@extends('layouts.admin')

@section('title', 'Sales Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Sales Management</h3>
                    <div>
                        <a href="{{ route('admin.sales.pos') }}" class="btn btn-success">
                            <i class="fas fa-cash-register"></i> POS Interface
                        </a>
                        <a href="{{ route('admin.sales.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> New Sale
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Sales Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-info">
                                <div class="inner">
                                    <h3>${{ number_format($todaySales, 2) }}</h3>
                                    <p>Today's Sales</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-success">
                                <div class="inner">
                                    <h3>${{ number_format($weekSales, 2) }}</h3>
                                    <p>This Week</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-calendar-week"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-warning">
                                <div class="inner">
                                    <h3>${{ number_format($monthSales, 2) }}</h3>
                                    <p>This Month</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-calendar-alt"></i>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-6">
                            <div class="small-box bg-danger">
                                <div class="inner">
                                    <h3>{{ $totalSales }}</h3>
                                    <p>Total Sales</p>
                                </div>
                                <div class="icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="dateFrom" placeholder="From Date">
                        </div>
                        <div class="col-md-3">
                            <input type="date" class="form-control" id="dateTo" placeholder="To Date">
                        </div>
                        <div class="col-md-3">
                            <select class="form-control" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="completed">Completed</option>
                                <option value="pending">Pending</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search sales...">
                        </div>
                    </div>

                    @if($sales->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="salesTable">
                                <thead>
                                    <tr>
                                        <th>Sale #</th>
                                        <th>Date</th>
                                        <th>Branch</th>
                                        <th>POS Device</th>
                                        <th>Items</th>
                                        <th>Subtotal</th>
                                        <th>Tax</th>
                                        <th>Discount</th>
                                        <th>Total</th>
                                        <th>Payment Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($sales as $sale)
                                        <tr data-status="{{ $sale->payment_status }}">
                                            <td>
                                                <strong>{{ $sale->sale_number }}</strong>
                                            </td>
                                            <td>{{ $sale->sale_date->format('M d, Y H:i') }}</td>
                                            <td>{{ $sale->branch->name }}</td>
                                            <td>{{ $sale->posDevice->device_name }}</td>
                                            <td>
                                                <span class="badge badge-info">{{ $sale->saleItems->count() }}</span>
                                            </td>
                                            <td>${{ number_format($sale->subtotal, 2) }}</td>
                                            <td>${{ number_format($sale->tax_amount, 2) }}</td>
                                            <td>${{ number_format($sale->discount_amount, 2) }}</td>
                                            <td><strong>${{ number_format($sale->total_amount, 2) }}</strong></td>
                                            <td>
                                                @switch($sale->payment_status)
                                                    @case('completed')
                                                        <span class="badge badge-success">Completed</span>
                                                        @break
                                                    @case('pending')
                                                        <span class="badge badge-warning">Pending</span>
                                                        @break
                                                    @case('cancelled')
                                                        <span class="badge badge-danger">Cancelled</span>
                                                        @break
                                                    @default
                                                        <span class="badge badge-secondary">{{ ucfirst($sale->payment_status) }}</span>
                                                @endswitch
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.sales.show', $sale) }}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.sales.receipt', $sale) }}" class="btn btn-sm btn-secondary" target="_blank">
                                                        <i class="fas fa-receipt"></i>
                                                    </a>
                                                    @if($sale->payment_status !== 'completed')
                                                        <a href="{{ route('admin.sales.edit', $sale) }}" class="btn btn-sm btn-warning">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $sales->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-cash-register fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Sales Found</h5>
                            <p class="text-muted">Start by creating your first sale.</p>
                            <a href="{{ route('admin.sales.pos') }}" class="btn btn-success">
                                <i class="fas fa-cash-register"></i> Open POS
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#salesTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

    // Status filter
    $('#statusFilter').on('change', function() {
        var status = $(this).val();
        $('#salesTable tbody tr').each(function() {
            if (status === '' || $(this).data('status') == status) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Date filters
    $('#dateFrom, #dateTo').on('change', function() {
        // Implement date filtering logic here
        filterByDate();
    });

    function filterByDate() {
        var dateFrom = $('#dateFrom').val();
        var dateTo = $('#dateTo').val();
        
        if (dateFrom || dateTo) {
            // You can implement AJAX filtering here or reload the page with date parameters
            var url = new URL(window.location);
            if (dateFrom) url.searchParams.set('date_from', dateFrom);
            if (dateTo) url.searchParams.set('date_to', dateTo);
            window.location = url;
        }
    }
});
</script>
@endpush
@endsection
