@extends('layouts.admin')

@section('title', 'Categories Management - ERP System')

@section('page-title', 'Categories Management')

@section('breadcrumb', 'Home > Admin > Categories')

@section('styles')
<style>
    .category-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border-left: 5px solid #667eea;
        margin-bottom: 20px;
    }
    
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .category-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .category-name {
        font-size: 20px;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
    }
    
    .category-brand {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .category-description {
        color: #718096;
        margin-bottom: 20px;
        line-height: 1.6;
    }
    
    .category-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
    }
    
    .stat-item {
        text-align: center;
        padding: 15px;
        background: #f7fafc;
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        transition: all 0.3s ease;
    }
    
    .stat-item:hover {
        border-color: #667eea;
        background: #edf2f7;
    }
    
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #2d3748;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 12px;
        color: #718096;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 600;
    }
    
    .category-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
    
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .status-active {
        background: #c6f6d5;
        color: #22543d;
    }
    
    .status-inactive {
        background: #fed7d7;
        color: #742a2a;
    }
    
    .header-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
        align-items: center;
    }
    
    .search-filters {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 30px;
    }
    
    .filter-row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr auto;
        gap: 20px;
        align-items: end;
    }
    
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    
    .filter-group label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 8px;
        font-size: 14px;
    }
    
    .filter-group input,
    .filter-group select {
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 14px;
        transition: all 0.3s ease;
    }
    
    .filter-group input:focus,
    .filter-group select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .filter-btn {
        padding: 12px 24px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .filter-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    }
    
    .empty-state-icon {
        font-size: 64px;
        margin-bottom: 20px;
        opacity: 0.5;
    }
    
    .empty-state-title {
        font-size: 24px;
        font-weight: 700;
        color: #2d3748;
        margin-bottom: 10px;
    }
    
    .empty-state-description {
        color: #718096;
        margin-bottom: 30px;
        line-height: 1.6;
    }
</style>
@endsection

@section('content')
<div class="header-actions">
    <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
        <i style="margin-right: 8px;">➕</i> Add New Category
    </a>
</div>

<!-- Search and Filters -->
<div class="search-filters">
    <form method="GET" action="{{ route('admin.categories.index') }}">
        <div class="filter-row">
            <div class="filter-group">
                <label for="search">Search Categories</label>
                <input type="text" id="search" name="search" placeholder="Search by name or description..." 
                       value="{{ request('search') }}">
            </div>
            
            <div class="filter-group">
                <label for="brand_id">Filter by Brand</label>
                <select id="brand_id" name="brand_id">
                    <option value="">All Brands</option>
                    @foreach($brands as $brand)
                        <option value="{{ $brand->id }}" {{ request('brand_id') == $brand->id ? 'selected' : '' }}>
                            {{ $brand->name }}
                        </option>
                    @endforeach
                </select>
            </div>
            
            <div class="filter-group">
                <label for="status">Status</label>
                <select id="status" name="status">
                    <option value="">All Status</option>
                    <option value="1" {{ request('status') === '1' ? 'selected' : '' }}>Active</option>
                    <option value="0" {{ request('status') === '0' ? 'selected' : '' }}>Inactive</option>
                </select>
            </div>
            
            <button type="submit" class="filter-btn">
                🔍 Filter
            </button>
        </div>
    </form>
</div>

<!-- Categories Grid -->
@if($categories->count() > 0)
    <div class="categories-grid">
        @foreach($categories as $category)
            <div class="category-card">
                <div class="category-header">
                    <h3 class="category-name">{{ $category->name }}</h3>
                    <div class="category-brand">{{ $category->brand->name }}</div>
                </div>
                
                @if($category->description)
                    <p class="category-description">{{ $category->description }}</p>
                @endif
                
                <div class="category-stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ $category->divisions->count() }}</div>
                        <div class="stat-label">Divisions</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">{{ $category->products->count() }}</div>
                        <div class="stat-label">Products</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">
                            <span class="status-badge {{ $category->is_active ? 'status-active' : 'status-inactive' }}">
                                {{ $category->is_active ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                        <div class="stat-label">Status</div>
                    </div>
                </div>
                
                <div class="category-actions">
                    <a href="{{ route('admin.categories.show', $category) }}" class="btn btn-secondary">
                        👁️ View
                    </a>
                    <a href="{{ route('admin.categories.edit', $category) }}" class="btn btn-warning">
                        ✏️ Edit
                    </a>
                    <form action="{{ route('admin.categories.destroy', $category) }}" method="POST" 
                          style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this category?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">
                            🗑️ Delete
                        </button>
                    </form>
                </div>
            </div>
        @endforeach
    </div>
    
    <!-- Pagination -->
    @if($categories->hasPages())
        <div style="margin-top: 30px; text-align: center;">
            {{ $categories->appends(request()->query())->links() }}
        </div>
    @endif
@else
    <div class="empty-state">
        <div class="empty-state-icon">📂</div>
        <h3 class="empty-state-title">No Categories Found</h3>
        <p class="empty-state-description">
            You haven't created any categories yet. Categories help organize your products into logical groups.
        </p>
        <a href="{{ route('admin.categories.create') }}" class="btn btn-primary">
            ➕ Create Your First Category
        </a>
    </div>
@endif
@endsection
