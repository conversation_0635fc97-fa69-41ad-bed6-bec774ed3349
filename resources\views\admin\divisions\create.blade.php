@extends('layouts.admin')

@section('title', 'Create Division')

@push('styles')
<style>
    .form-wizard {
        background: var(--card-bg);
        border-radius: 20px;
        box-shadow: 0 20px 60px var(--card-shadow);
        overflow: hidden;
        transition: all 0.3s ease;
        margin: 2rem auto;
        max-width: 1200px;
    }

    .wizard-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 3rem 2rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .wizard-header::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 4s ease-in-out infinite;
    }

    @keyframes shimmer {
        0%, 100% { transform: rotate(0deg); }
        50% { transform: rotate(180deg); }
    }

    .wizard-title {
        font-size: 2.5rem;
        font-weight: 800;
        margin: 0;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .wizard-subtitle {
        opacity: 0.9;
        margin-top: 0.5rem;
        position: relative;
        z-index: 1;
        font-size: 1.1rem;
    }

    .form-sections {
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 600px;
    }

    .form-section {
        padding: 3rem;
        background: var(--card-bg);
        border-right: 1px solid var(--border-color);
    }

    .form-section:last-child {
        border-right: none;
        background: var(--bg-primary);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .section-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
    }

    .form-group {
        margin-bottom: 2rem;
    }

    .form-label {
        display: block;
        margin-bottom: 0.75rem;
        font-weight: 700;
        color: var(--text-primary);
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .required-indicator {
        color: #e53e3e;
        margin-left: 5px;
    }

    .form-control {
        width: 100%;
        padding: 1rem 1.25rem;
        border: 3px solid var(--border-color);
        border-radius: 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: var(--bg-secondary);
        color: var(--text-primary);
        font-weight: 500;
    }

    .form-control:focus {
        outline: none;
        border-color: #f093fb;
        box-shadow: 0 0 0 4px rgba(240, 147, 251, 0.15);
        transform: translateY(-2px);
    }

    .form-control:hover {
        border-color: #a0aec0;
        transform: translateY(-1px);
    }

    .form-control.is-invalid {
        border-color: #e53e3e;
        box-shadow: 0 0 0 4px rgba(229, 62, 62, 0.15);
    }

    .invalid-feedback {
        color: #e53e3e;
        font-size: 0.9rem;
        margin-top: 0.5rem;
        font-weight: 600;
    }

    .form-help {
        font-size: 0.85rem;
        color: var(--text-secondary);
        margin-top: 0.5rem;
        font-style: italic;
    }

    .color-input-group {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .color-preview {
        width: 60px;
        height: 50px;
        border-radius: 12px;
        border: 3px solid var(--border-color);
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .color-preview:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(0,0,0,0.2);
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: var(--bg-secondary);
        border-radius: 12px;
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .form-check:hover {
        border-color: #f093fb;
        background: rgba(240, 147, 251, 0.05);
    }

    .form-check-input {
        width: 20px;
        height: 20px;
        accent-color: #f093fb;
    }

    .form-check-label {
        font-weight: 600;
        color: var(--text-primary);
        cursor: pointer;
        margin: 0;
    }

    .wizard-actions {
        padding: 2.5rem 3rem;
        background: var(--bg-primary);
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .btn {
        padding: 1rem 2.5rem;
        border-radius: 15px;
        font-weight: 700;
        text-decoration: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(240, 147, 251, 0.4);
    }

    .btn-primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(240, 147, 251, 0.5);
    }

    .btn-secondary {
        background: var(--card-bg);
        color: var(--text-primary);
        border: 3px solid var(--border-color);
    }

    .btn-secondary:hover {
        background: var(--bg-primary);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px var(--card-shadow);
    }

    @media (max-width: 768px) {
        .form-sections {
            grid-template-columns: 1fr;
        }

        .form-section {
            border-right: none;
            border-bottom: 1px solid var(--border-color);
            padding: 2rem;
        }

        .form-section:last-child {
            border-bottom: none;
        }

        .wizard-actions {
            flex-direction: column;
            gap: 1rem;
            padding: 2rem;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }

        .wizard-title {
            font-size: 2rem;
        }

        .wizard-header {
            padding: 2rem;
        }
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-12">
            <div class="form-wizard">
                <!-- Wizard Header -->
                <div class="wizard-header">
                    <h1 class="wizard-title">Create New Division</h1>
                    <p class="wizard-subtitle">Organize products within categories using divisions</p>
                </div>

                <form action="{{ route('admin.divisions.store') }}" method="POST">
                    @csrf

                    <!-- Form Sections -->
                    <div class="form-sections">
                        <!-- Basic Information Section -->
                        <div class="form-section">
                            <div class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                Basic Information
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="category_id">
                                    Category <span class="required-indicator">*</span>
                                </label>
                                <select name="category_id" id="category_id" class="form-control @error('category_id') is-invalid @enderror" required>
                                    <option value="">Select Category</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->brand->name }} - {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('category_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-help">Choose the category this division belongs to</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="name">
                                    Division Name <span class="required-indicator">*</span>
                                </label>
                                <input type="text" name="name" id="name" class="form-control @error('name') is-invalid @enderror"
                                       value="{{ old('name') }}" required placeholder="Enter division name">
                                @error('name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-help">A descriptive name for this division</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="description">Description</label>
                                <textarea name="description" id="description" class="form-control @error('description') is-invalid @enderror"
                                          rows="4" placeholder="Optional description for this division">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-help">Detailed description of what this division contains</div>
                            </div>
                        </div>

                        <!-- Display Settings Section -->
                        <div class="form-section">
                            <div class="section-title">
                                <div class="section-icon">
                                    <i class="fas fa-palette"></i>
                                </div>
                                Display Settings
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="color">Division Color</label>
                                <div class="color-input-group">
                                    <input type="color" name="color" id="color" class="form-control @error('color') is-invalid @enderror"
                                           value="{{ old('color', '#f093fb') }}" style="width: 80px; height: 50px;">
                                    <div class="color-preview" id="colorPreview" style="background-color: {{ old('color', '#f093fb') }};"></div>
                                </div>
                                @error('color')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-help">Color used for visual identification</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="icon">Icon Class</label>
                                <input type="text" name="icon" id="icon" class="form-control @error('icon') is-invalid @enderror"
                                       value="{{ old('icon') }}" placeholder="fas fa-folder">
                                @error('icon')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-help">Font Awesome icon class (e.g., fas fa-folder)</div>
                            </div>

                            <div class="form-group">
                                <label class="form-label" for="sort_order">Sort Order</label>
                                <input type="number" name="sort_order" id="sort_order" class="form-control @error('sort_order') is-invalid @enderror"
                                       value="{{ old('sort_order', 0) }}" min="0" placeholder="0">
                                @error('sort_order')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-help">Lower numbers appear first in lists</div>
                            </div>

                            <div class="form-group">
                                <div class="form-check">
                                    <input type="checkbox" name="is_active" id="is_active" class="form-check-input"
                                           value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        Active Division
                                    </label>
                                </div>
                                <div class="form-help">Inactive divisions won't be visible in the system</div>
                                @error('is_active')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Wizard Actions -->
                    <div class="wizard-actions">
                        <a href="{{ route('admin.divisions.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Divisions
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Create Division
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Color preview update
    $('#color').on('change', function() {
        $('#colorPreview').css('background-color', $(this).val());
    });

    // Form validation feedback
    $('.form-control').on('blur', function() {
        if ($(this).val() && $(this).hasClass('is-invalid')) {
            $(this).removeClass('is-invalid');
        }
    });
});
</script>
@endpush
@endsection