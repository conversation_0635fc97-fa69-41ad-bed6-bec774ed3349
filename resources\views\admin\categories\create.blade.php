@extends('layouts.admin')

@section('title', 'Create Category - ERP System')

@section('page-title', 'Create New Category')

@section('breadcrumb', 'Home > Admin > Categories > Create')

@section('styles')
<style>
    .form-container {
        background: white;
        padding: 35px;
        border-radius: 15px;
        box-shadow: 0 4px 25px rgba(0,0,0,0.08);
        margin-bottom: 25px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 25px;
        margin-bottom: 25px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group.full-width {
        grid-column: 1 / -1;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #2d3748;
        font-size: 14px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 14px;
        transition: all 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }
    
    .form-group small {
        color: #718096;
        font-size: 12px;
        margin-top: 5px;
        display: block;
    }
    
    .color-input-group {
        display: flex;
        gap: 15px;
        align-items: center;
    }
    
    .color-preview {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .color-preview:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .icon-selector {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 10px;
        margin-top: 10px;
        padding: 15px;
        background: #f7fafc;
        border-radius: 10px;
        border: 2px solid #e2e8f0;
    }
    
    .icon-option {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        cursor: pointer;
        font-size: 20px;
        transition: all 0.3s ease;
    }
    
    .icon-option:hover,
    .icon-option.selected {
        border-color: #667eea;
        background: #edf2f7;
        transform: scale(1.1);
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-top: 10px;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: 20px;
        height: 20px;
        accent-color: #667eea;
    }
    
    .checkbox-group label {
        margin: 0;
        font-weight: 500;
        cursor: pointer;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 35px;
        padding-top: 25px;
        border-top: 2px solid #f1f3f4;
    }
    
    .header-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 25px;
        align-items: center;
    }
    
    .preview-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 25px rgba(0,0,0,0.08);
        border-left: 5px solid #667eea;
    }
    
    .preview-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .preview-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: #667eea;
    }
    
    .preview-name {
        font-size: 20px;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
    }
    
    .preview-description {
        color: #718096;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    
    .preview-brand {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
    }
</style>
@endsection

@section('content')
<div class="header-actions">
    <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary">
        <i style="margin-right: 8px;">←</i> Back to Categories
    </a>
</div>

<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 30px;">
    <!-- Form Section -->
    <div class="form-container">
        <h3 style="margin: 0 0 25px 0; color: #2d3748; font-size: 20px;">Category Information</h3>
        
        <form action="{{ route('admin.categories.store') }}" method="POST" id="categoryForm">
            @csrf
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="name">Category Name *</label>
                    <input type="text" id="name" name="name" value="{{ old('name') }}" 
                           placeholder="Enter category name" required>
                    <small>Choose a descriptive name for your category</small>
                    @error('name')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label for="brand_id">Brand *</label>
                    <select id="brand_id" name="brand_id" required>
                        <option value="">Select a brand</option>
                        @foreach($brands as $brand)
                            <option value="{{ $brand->id }}" {{ old('brand_id') == $brand->id ? 'selected' : '' }}>
                                {{ $brand->name }}
                            </option>
                        @endforeach
                    </select>
                    @error('brand_id')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            
            <div class="form-group full-width">
                <label for="description">Description</label>
                <textarea id="description" name="description" placeholder="Describe this category...">{{ old('description') }}</textarea>
                <small>Optional description to help identify this category</small>
                @error('description')
                    <div class="error">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="color">Category Color</label>
                    <div class="color-input-group">
                        <input type="color" id="color" name="color" value="{{ old('color', '#667eea') }}">
                        <div class="color-preview" id="colorPreview" style="background-color: {{ old('color', '#667eea') }};"></div>
                        <span>Choose a color theme</span>
                    </div>
                    @error('color')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label>Status</label>
                    <div class="checkbox-group">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               {{ old('is_active', true) ? 'checked' : '' }}>
                        <label for="is_active">Active Category</label>
                    </div>
                    <small>Inactive categories won't appear in POS systems</small>
                </div>
            </div>
            
            <div class="form-group">
                <label for="icon">Category Icon</label>
                <input type="hidden" id="icon" name="icon" value="{{ old('icon', '📂') }}">
                <div class="icon-selector">
                    @php
                        $icons = ['📂', '🍔', '🍕', '☕', '🥤', '🍰', '🥗', '🍜', '🍖', '🐟', '🥖', '🧀', '🍎', '🥕', '🌶️', '🍯'];
                    @endphp
                    @foreach($icons as $iconOption)
                        <div class="icon-option {{ old('icon', '📂') == $iconOption ? 'selected' : '' }}" 
                             data-icon="{{ $iconOption }}">
                            {{ $iconOption }}
                        </div>
                    @endforeach
                </div>
                <small>Select an icon to represent this category</small>
                @error('icon')
                    <div class="error">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary" style="padding: 14px 28px; font-weight: 600;">
                    <i style="margin-right: 8px;">✓</i> Create Category
                </button>
                <a href="{{ route('admin.categories.index') }}" class="btn btn-secondary" style="padding: 14px 28px;">
                    Cancel
                </a>
            </div>
        </form>
    </div>
    
    <!-- Preview Section -->
    <div class="preview-card">
        <h3 style="margin: 0 0 20px 0; color: #2d3748; font-size: 18px;">Preview</h3>
        
        <div class="preview-header">
            <div class="preview-icon" id="previewIcon">📂</div>
            <div>
                <h4 class="preview-name" id="previewName">Category Name</h4>
                <div class="preview-brand" id="previewBrand">Select Brand</div>
            </div>
        </div>
        
        <div class="preview-description" id="previewDescription">
            Category description will appear here...
        </div>
        
        <div style="font-size: 12px; color: #718096; margin-top: 20px;">
            <strong>Note:</strong> This is how your category will appear in the system.
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Real-time preview updates
    document.getElementById('name').addEventListener('input', function() {
        document.getElementById('previewName').textContent = this.value || 'Category Name';
    });
    
    document.getElementById('description').addEventListener('input', function() {
        document.getElementById('previewDescription').textContent = this.value || 'Category description will appear here...';
    });
    
    document.getElementById('brand_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        document.getElementById('previewBrand').textContent = selectedOption.text;
    });
    
    document.getElementById('color').addEventListener('input', function() {
        const color = this.value;
        document.getElementById('colorPreview').style.backgroundColor = color;
        document.getElementById('previewIcon').style.backgroundColor = color;
        document.querySelector('.preview-card').style.borderLeftColor = color;
    });
    
    // Icon selection
    document.querySelectorAll('.icon-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            document.querySelectorAll('.icon-option').forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked option
            this.classList.add('selected');
            
            // Update hidden input and preview
            const icon = this.dataset.icon;
            document.getElementById('icon').value = icon;
            document.getElementById('previewIcon').textContent = icon;
        });
    });
</script>
@endsection
