<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Brand - ERP System</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f9f9f9; padding: 20px; margin: 0; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .header h1 { margin: 0; color: #333; }
        .form-container { background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: #007bff; }
        .form-group small { color: #666; font-size: 12px; }
        .error { color: #dc3545; font-size: 12px; margin-top: 5px; }
        .btn { padding: 12px 24px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; cursor: pointer; font-size: 14px; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .company-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 10px; }
        .company-details { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Add New Brand</h1>
        </div>

        @if($errors->any())
            <div class="alert alert-danger">
                <strong>Please fix the following errors:</strong>
                <ul style="margin: 10px 0 0 20px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="form-container">
            <form method="POST" action="{{ route('admin.brands.store') }}">
                @csrf
                
                <div class="form-group">
                    <label for="company_id">Company *</label>
                    <select name="company_id" id="company_id" required onchange="showCompanyInfo()">
                        <option value="">Select Company</option>
                        @foreach($companies as $company)
                            <option value="{{ $company->id }}" 
                                    data-license="{{ $company->license_expiry > now() ? 'Active' : 'Expired' }}"
                                    data-expiry="{{ $company->license_expiry->format('M d, Y') }}"
                                    data-brands="{{ $company->brands->count() }}"
                                    {{ old('company_id', $selectedCompany) == $company->id ? 'selected' : '' }}>
                                {{ $company->name }}
                            </option>
                        @endforeach
                    </select>
                    <small>Select the company this brand belongs to</small>
                    @error('company_id')
                        <div class="error">{{ $message }}</div>
                    @enderror
                    
                    <div id="companyInfo" class="company-info" style="display: none;">
                        <h4 style="margin: 0 0 10px 0;">Company Information</h4>
                        <div class="company-details">
                            <p style="margin: 5px 0;"><strong>License Status:</strong> <span id="licenseStatus"></span></p>
                            <p style="margin: 5px 0;"><strong>License Expiry:</strong> <span id="licenseExpiry"></span></p>
                            <p style="margin: 5px 0;"><strong>Existing Brands:</strong> <span id="brandCount"></span></p>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="name">Brand Name *</label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" required>
                    <small>Enter a unique name for this brand</small>
                    @error('name')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="description">Description</label>
                    <textarea name="description" id="description" rows="4">{{ old('description') }}</textarea>
                    <small>Optional description of the brand</small>
                    @error('description')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <button type="submit" class="btn btn-primary">Create Brand</button>
                    <a href="{{ route('admin.brands.index') }}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>

        <!-- Brand Planning Info -->
        <div class="form-container" style="margin-top: 20px;">
            <h3 style="margin: 0 0 15px 0;">What's Next?</h3>
            <p style="color: #666; margin-bottom: 15px;">
                After creating this brand, you'll be able to:
            </p>
            <ul style="color: #666; margin-left: 20px;">
                <li>Create product categories for this brand</li>
                <li>Set up branches for different locations</li>
                <li>Assign users to manage this brand</li>
                <li>Configure POS devices for branches</li>
            </ul>
        </div>
    </div>

    <script>
        function showCompanyInfo() {
            const select = document.getElementById('company_id');
            const info = document.getElementById('companyInfo');
            const details = document.querySelector('.company-details');
            
            if (select.value) {
                const option = select.options[select.selectedIndex];
                const licenseStatus = option.getAttribute('data-license');
                const expiry = option.getAttribute('data-expiry');
                const brandCount = option.getAttribute('data-brands');
                
                document.getElementById('licenseStatus').textContent = licenseStatus;
                document.getElementById('licenseStatus').style.color = licenseStatus === 'Active' ? '#28a745' : '#dc3545';
                document.getElementById('licenseExpiry').textContent = expiry;
                document.getElementById('brandCount').textContent = brandCount;
                
                info.style.display = 'block';
                details.style.display = 'block';
                
                // Show warning if license expired
                if (licenseStatus === 'Expired') {
                    info.style.borderLeft = '4px solid #dc3545';
                    info.style.background = '#f8d7da';
                } else {
                    info.style.borderLeft = '4px solid #28a745';
                    info.style.background = '#d4edda';
                }
            } else {
                info.style.display = 'none';
                details.style.display = 'none';
            }
        }

        // Show company info on page load if company is pre-selected
        document.addEventListener('DOMContentLoaded', function() {
            showCompanyInfo();
        });
    </script>
</body>
</html>
