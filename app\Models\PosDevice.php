<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PosDevice extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'device_name',
        'device_serial',
        'device_model',
        'device_info',
        'status'
    ];

    protected $casts = [
        'device_info' => 'array',
    ];

    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function sales()
    {
        return $this->hasMany(Sale::class);
    }

    public function syncLogs()
    {
        return $this->hasMany(SyncLog::class);
    }

    /**
     * Check if device is active
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Get device's company through branch
     */
    public function getCompanyAttribute()
    {
        return $this->branch->company ?? null;
    }

    /**
     * Scope for active devices
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for devices by company
     */
    public function scopeByCompany($query, $companyId)
    {
        return $query->whereHas('branch', function($q) use ($companyId) {
            $q->where('company_id', $companyId);
        });
    }
}
