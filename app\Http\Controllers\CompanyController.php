<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Company;

class CompanyController extends Controller
{
    public function index() {
        return Company::all();
    }

    public function store(Request $request) {
        $validated = $request->validate([
            'name' => 'required',
            'license_key' => 'required|unique:companies',
            'license_expiry' => 'required|date',
            'pos_limit' => 'required|integer'
        ]);
        return Company::create($validated);
    }

    public function show(Company $company) {
        return $company;
    }

    public function update(Request $request, Company $company) {
        $company->update($request->all());
        return $company;
    }

    public function destroy(Company $company) {
        $company->delete();
        return response()->noContent();
    }
}