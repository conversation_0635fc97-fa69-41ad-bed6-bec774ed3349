<?php $__env->startSection('title', 'Create Category - ERP System'); ?>

<?php $__env->startSection('page-title', 'Create New Category'); ?>

<?php $__env->startSection('breadcrumb', 'Home > Admin > Categories > Create'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .form-container {
        background: white;
        padding: 35px;
        border-radius: 15px;
        box-shadow: 0 4px 25px rgba(0,0,0,0.08);
        margin-bottom: 25px;
    }
    
    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 25px;
        margin-bottom: 25px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group.full-width {
        grid-column: 1 / -1;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #2d3748;
        font-size: 14px;
    }
    
    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        font-size: 14px;
        transition: all 0.3s ease;
    }
    
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }
    
    .form-group small {
        color: #718096;
        font-size: 12px;
        margin-top: 5px;
        display: block;
    }
    
    .color-input-group {
        display: flex;
        gap: 15px;
        align-items: center;
    }
    
    .color-preview {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .color-preview:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }
    
    .icon-selector {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 10px;
        margin-top: 10px;
        padding: 15px;
        background: #f7fafc;
        border-radius: 10px;
        border: 2px solid #e2e8f0;
    }
    
    .icon-option {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        cursor: pointer;
        font-size: 20px;
        transition: all 0.3s ease;
    }
    
    .icon-option:hover,
    .icon-option.selected {
        border-color: #667eea;
        background: #edf2f7;
        transform: scale(1.1);
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-top: 10px;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: 20px;
        height: 20px;
        accent-color: #667eea;
    }
    
    .checkbox-group label {
        margin: 0;
        font-weight: 500;
        cursor: pointer;
    }
    
    .form-actions {
        display: flex;
        gap: 15px;
        justify-content: flex-end;
        margin-top: 35px;
        padding-top: 25px;
        border-top: 2px solid #f1f3f4;
    }
    
    .header-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 25px;
        align-items: center;
    }
    
    .preview-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 25px rgba(0,0,0,0.08);
        border-left: 5px solid #667eea;
    }
    
    .preview-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
    }
    
    .preview-icon {
        width: 50px;
        height: 50px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: #667eea;
    }
    
    .preview-name {
        font-size: 20px;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
    }
    
    .preview-description {
        color: #718096;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    
    .preview-brand {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        display: inline-block;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="header-actions">
    <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-secondary">
        <i style="margin-right: 8px;">←</i> Back to Categories
    </a>
</div>

<div style="display: grid; grid-template-columns: 2fr 1fr; gap: 30px;">
    <!-- Form Section -->
    <div class="form-container">
        <h3 style="margin: 0 0 25px 0; color: #2d3748; font-size: 20px;">Category Information</h3>
        
        <form action="<?php echo e(route('admin.categories.store')); ?>" method="POST" id="categoryForm">
            <?php echo csrf_field(); ?>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="name">Category Name *</label>
                    <input type="text" id="name" name="name" value="<?php echo e(old('name')); ?>" 
                           placeholder="Enter category name" required>
                    <small>Choose a descriptive name for your category</small>
                    <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="error"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <div class="form-group">
                    <label for="brand_id">Brand *</label>
                    <select id="brand_id" name="brand_id" required>
                        <option value="">Select a brand</option>
                        <?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($brand->id); ?>" <?php echo e(old('brand_id') == $brand->id ? 'selected' : ''); ?>>
                                <?php echo e($brand->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                    <?php $__errorArgs = ['brand_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="error"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>
            
            <div class="form-group full-width">
                <label for="description">Description</label>
                <textarea id="description" name="description" placeholder="Describe this category..."><?php echo e(old('description')); ?></textarea>
                <small>Optional description to help identify this category</small>
                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            
            <div class="form-grid">
                <div class="form-group">
                    <label for="color">Category Color</label>
                    <div class="color-input-group">
                        <input type="color" id="color" name="color" value="<?php echo e(old('color', '#667eea')); ?>">
                        <div class="color-preview" id="colorPreview" style="background-color: <?php echo e(old('color', '#667eea')); ?>;"></div>
                        <span>Choose a color theme</span>
                    </div>
                    <?php $__errorArgs = ['color'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="error"><?php echo e($message); ?></div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
                
                <div class="form-group">
                    <label>Status</label>
                    <div class="checkbox-group">
                        <input type="checkbox" id="is_active" name="is_active" value="1" 
                               <?php echo e(old('is_active', true) ? 'checked' : ''); ?>>
                        <label for="is_active">Active Category</label>
                    </div>
                    <small>Inactive categories won't appear in POS systems</small>
                </div>
            </div>
            
            <div class="form-group">
                <label for="icon">Category Icon</label>
                <input type="hidden" id="icon" name="icon" value="<?php echo e(old('icon', '📂')); ?>">
                <div class="icon-selector">
                    <?php
                        $icons = ['📂', '🍔', '🍕', '☕', '🥤', '🍰', '🥗', '🍜', '🍖', '🐟', '🥖', '🧀', '🍎', '🥕', '🌶️', '🍯'];
                    ?>
                    <?php $__currentLoopData = $icons; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $iconOption): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="icon-option <?php echo e(old('icon', '📂') == $iconOption ? 'selected' : ''); ?>" 
                             data-icon="<?php echo e($iconOption); ?>">
                            <?php echo e($iconOption); ?>

                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <small>Select an icon to represent this category</small>
                <?php $__errorArgs = ['icon'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                    <div class="error"><?php echo e($message); ?></div>
                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary" style="padding: 14px 28px; font-weight: 600;">
                    <i style="margin-right: 8px;">✓</i> Create Category
                </button>
                <a href="<?php echo e(route('admin.categories.index')); ?>" class="btn btn-secondary" style="padding: 14px 28px;">
                    Cancel
                </a>
            </div>
        </form>
    </div>
    
    <!-- Preview Section -->
    <div class="preview-card">
        <h3 style="margin: 0 0 20px 0; color: #2d3748; font-size: 18px;">Preview</h3>
        
        <div class="preview-header">
            <div class="preview-icon" id="previewIcon">📂</div>
            <div>
                <h4 class="preview-name" id="previewName">Category Name</h4>
                <div class="preview-brand" id="previewBrand">Select Brand</div>
            </div>
        </div>
        
        <div class="preview-description" id="previewDescription">
            Category description will appear here...
        </div>
        
        <div style="font-size: 12px; color: #718096; margin-top: 20px;">
            <strong>Note:</strong> This is how your category will appear in the system.
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Real-time preview updates
    document.getElementById('name').addEventListener('input', function() {
        document.getElementById('previewName').textContent = this.value || 'Category Name';
    });
    
    document.getElementById('description').addEventListener('input', function() {
        document.getElementById('previewDescription').textContent = this.value || 'Category description will appear here...';
    });
    
    document.getElementById('brand_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        document.getElementById('previewBrand').textContent = selectedOption.text;
    });
    
    document.getElementById('color').addEventListener('input', function() {
        const color = this.value;
        document.getElementById('colorPreview').style.backgroundColor = color;
        document.getElementById('previewIcon').style.backgroundColor = color;
        document.querySelector('.preview-card').style.borderLeftColor = color;
    });
    
    // Icon selection
    document.querySelectorAll('.icon-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            document.querySelectorAll('.icon-option').forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked option
            this.classList.add('selected');
            
            // Update hidden input and preview
            const icon = this.dataset.icon;
            document.getElementById('icon').value = icon;
            document.getElementById('previewIcon').textContent = icon;
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\erp-pos-system\resources\views/admin/categories/create.blade.php ENDPATH**/ ?>