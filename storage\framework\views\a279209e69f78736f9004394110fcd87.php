

<?php $__env->startSection('title', 'Divisions Management - ERP System'); ?>

<?php $__env->startSection('page-title', 'Divisions Management'); ?>

<?php $__env->startSection('breadcrumb', 'Home > Admin > Divisions'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .division-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border-left: 5px solid #667eea;
        margin-bottom: 20px;
    }
    .division-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    .division-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .division-name {
        font-size: 20px;
        font-weight: 700;
        color: #2d3748;
        margin: 0;
    }
    .division-category {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 6px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .division-description {
        color: #718096;
        margin-bottom: 20px;
        line-height: 1.6;
    }
    .division-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    .status-active {
        background: #c6f6d5;
        color: #22543d;
    }
    .status-inactive {
        background: #fed7d7;
        color: #742a2a;
    }
    .header-actions {
        display: flex;
        gap: 15px;
        margin-bottom: 30px;
        align-items: center;
    }
    .search-filters {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        margin-bottom: 30px;
    }
    .filter-row {
        display: grid;
        grid-template-columns: 1fr 1fr auto;
        gap: 20px;
        align-items: end;
    }
    .filter-group {
        display: flex;
        flex-direction: column;
    }
    .filter-group label {
        font-weight: 600;
        color: #2d3748;
    }
    .filter-group select {
        width: 100%;
        padding: 10px;
        border-radius: 8px;
        border: 2px solid #e2e8f0;
        font-size: 14px;
        margin-top: 5px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="header-actions">
    <a href="<?php echo e(route('admin.divisions.create')); ?>" class="btn btn-primary">
        <i style="margin-right: 8px;">＋</i> Create Division
    </a>
</div>

<div class="search-filters">
    <form method="GET" action="<?php echo e(route('admin.divisions.index')); ?>">
        <div class="filter-row">
            <div class="filter-group">
                <label for="category_id">Filter by Category</label>
                <select id="category_id" name="category_id">
                    <option value="">All Categories</option>
                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($category->id); ?>" <?php echo e(request('category_id') == $category->id ? 'selected' : ''); ?>>
                            <?php echo e($category->name); ?>

                        </option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </select>
            </div>
            <div class="filter-group">
                <label for="status">Status</label>
                <select id="status" name="status">
                    <option value="">All Status</option>
                    <option value="1" <?php echo e(request('status') == '1' ? 'selected' : ''); ?>>Active</option>
                    <option value="0" <?php echo e(request('status') == '0' ? 'selected' : ''); ?>>Inactive</option>
                </select>
            </div>
            <button type="submit" class="btn btn-secondary">Filter</button>
        </div>
    </form>
</div>

<?php $__currentLoopData = $divisions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $division): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="division-card">
        <div class="division-header">
            <h3 class="division-name"><?php echo e($division->name); ?></h3>
            <span class="division-category"><?php echo e($division->category->name ?? 'N/A'); ?></span>
        </div>
        <div class="division-description">
            <?php echo e($division->description ?? 'No description.'); ?>

        </div>
        <div>
            <span class="status-badge <?php echo e($division->is_active ? 'status-active' : 'status-inactive'); ?>">
                <?php echo e($division->is_active ? 'Active' : 'Inactive'); ?>

            </span>
        </div>
        <div class="division-actions">
            <a href="<?php echo e(route('admin.divisions.edit', $division->id)); ?>" class="btn btn-sm btn-primary">Edit</a>
            <a href="<?php echo e(route('admin.divisions.show', $division->id)); ?>" class="btn btn-sm btn-secondary">View</a>
        </div>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php if($divisions->isEmpty()): ?>
    <div style="text-align:center; color:#718096; margin-top:40px;">
        No divisions found.
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\erp-pos-system\resources\views/admin/divisions/create.blade.php ENDPATH**/ ?>