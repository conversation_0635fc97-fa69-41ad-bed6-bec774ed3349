<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Category;
use App\Models\Brand;

class CategoryController extends Controller
{
    /**
     * Get categories with access control
     */
    public function index(Request $request)
    {
            $user = Auth::user();

    if ($user->isMasterAdmin()) {
        $categories = Category::with(['brand.company', 'divisions'])->get();
        $brands = Brand::with('company')->get();
    } else {
        $categories = Category::whereHas('brand', function($q) use ($user) {
            $q->where('company_id', $user->company_id);
        })->with(['brand', 'divisions'])->get();

        $brands = Brand::where('company_id', $user->company_id)->get();
    }

    return view('admin.categories.index', compact('categories', 'brands'));

        // Filter by brand if specified
        if ($request->has('brand_id')) {
            $query->where('brand_id', $request->brand_id);
        }

        return response()->json([
            'success' => true,
            'data' => $query->get()
        ]);
    }

    /**
     * Create new category
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'brand_id' => 'required|exists:brands,id',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        $user = Auth::user();

        // Check if user can create categories for this brand
        if (!$user->isMasterAdmin()) {
            $brand = Brand::find($validated['brand_id']);
            if ($brand->company_id !== $user->company_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only create categories for your company brands.'
                ], 403);
            }
        }

        $validated['is_active'] = $validated['is_active'] ?? true;
        $category = Category::create($validated);

        return response()->json([
            'success' => true,
            'data' => $category->load('brand'),
            'message' => 'Category created successfully.'
        ], 201);
    }

    /**
     * Show specific category
     */
    public function show(Category $category)
    {
        $user = Auth::user();

        // Check access
        if (!$user->isMasterAdmin() && $category->brand->company_id !== $user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $category->load(['brand.company', 'divisions.groups.products'])
        ]);
    }

    /**
     * Update category
     */
    public function update(Request $request, Category $category)
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'brand_id' => 'sometimes|required|exists:brands,id',
            'description' => 'nullable|string',
            'is_active' => 'boolean'
        ]);

        $user = Auth::user();

        // Check access
        if (!$user->isMasterAdmin() && $category->brand->company_id !== $user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.'
            ], 403);
        }

        $category->update($validated);

        return response()->json([
            'success' => true,
            'data' => $category->load('brand'),
            'message' => 'Category updated successfully.'
        ]);
    }

    /**
     * Delete category
     */
    public function destroy(Category $category)
    {
        $user = Auth::user();

        // Check access
        if (!$user->isMasterAdmin() && $category->brand->company_id !== $user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.'
            ], 403);
        }

        // Check if category has divisions
        if ($category->divisions()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete category with existing divisions.'
            ], 400);
        }

        $category->delete();

        return response()->json([
            'success' => true,
            'message' => 'Category deleted successfully.'
        ]);
    }
}
