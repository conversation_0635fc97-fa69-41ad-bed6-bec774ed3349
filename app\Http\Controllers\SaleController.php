<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\Sale;
use App\Models\SaleItem;
use App\Models\Product;
use App\Models\PosDevice;

class SaleController extends Controller
{
    /**
     * Get sales with access control and filtering
     */
    public function index(Request $request)
    {
        $user = Auth::user();

        // Master admin sees all sales
        if ($user->isMasterAdmin()) {
            $query = Sale::with(['branch.company', 'user', 'posDevice', 'saleItems.product', 'payments']);
        } else {
            // Regular users see only their company's sales
            $query = Sale::whereHas('branch', function($q) use ($user) {
                $q->where('company_id', $user->company_id);
            })->with(['branch', 'user', 'posDevice', 'saleItems.product', 'payments']);
        }

        // Apply filters
        if ($request->has('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        if ($request->has('pos_device_id')) {
            $query->where('pos_device_id', $request->pos_device_id);
        }

        if ($request->has('payment_status')) {
            $query->where('payment_status', $request->payment_status);
        }

        if ($request->has('date_from')) {
            $query->whereDate('sale_date', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('sale_date', '<=', $request->date_to);
        }

        // Pagination
        $perPage = $request->get('per_page', 15);
        $sales = $query->orderBy('sale_date', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $sales
        ]);
    }

    /**
     * Create new sale with items
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'pos_device_id' => 'required|exists:pos_devices,id',
            'branch_id' => 'required|exists:branches,id',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.product_id' => 'required|exists:products,id',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.unit_price' => 'required|numeric|min:0',
            'items.*.discount_amount' => 'nullable|numeric|min:0',
            'payments' => 'nullable|array',
            'payments.*.method' => 'required|in:cash,card,bank,wallet',
            'payments.*.amount' => 'required|numeric|min:0'
        ]);

        $user = Auth::user();

        // Check if user can create sales for this branch
        if (!$user->isMasterAdmin()) {
            $posDevice = PosDevice::find($validated['pos_device_id']);
            if ($posDevice->branch->company_id !== $user->company_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only create sales for your company branches.'
                ], 403);
            }
        }

        DB::beginTransaction();

        try {
            // Generate sale number
            $saleNumber = 'SALE-' . date('Ymd') . '-' . str_pad(Sale::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);

            // Create sale
            $sale = Sale::create([
                'sale_number' => $saleNumber,
                'pos_device_id' => $validated['pos_device_id'],
                'branch_id' => $validated['branch_id'],
                'user_id' => $user->id,
                'tax_amount' => $validated['tax_amount'] ?? 0,
                'discount_amount' => $validated['discount_amount'] ?? 0,
                'sale_date' => now(),
                'notes' => $validated['notes'] ?? null,
                'payment_status' => 'pending'
            ]);

            $subtotal = 0;

            // Create sale items
            foreach ($validated['items'] as $item) {
                $product = Product::find($item['product_id']);

                // Check stock availability
                if ($product->stock_quantity !== null && $product->stock_quantity < $item['quantity']) {
                    throw new \Exception("Insufficient stock for product: {$product->name}");
                }

                $itemTotal = ($item['unit_price'] * $item['quantity']) - ($item['discount_amount'] ?? 0);

                SaleItem::create([
                    'sale_id' => $sale->id,
                    'product_id' => $item['product_id'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $itemTotal,
                    'discount_amount' => $item['discount_amount'] ?? 0
                ]);

                // Update product stock
                if ($product->stock_quantity !== null) {
                    $product->decrement('stock_quantity', $item['quantity']);
                }

                $subtotal += $itemTotal;
            }

            // Update sale totals
            $totalAmount = $subtotal + $sale->tax_amount - $sale->discount_amount;
            $sale->update([
                'subtotal' => $subtotal,
                'total_amount' => $totalAmount
            ]);

            // Process payments if provided
            if (!empty($validated['payments'])) {
                $totalPaid = 0;
                foreach ($validated['payments'] as $payment) {
                    $sale->payments()->create([
                        'method' => $payment['method'],
                        'amount' => $payment['amount']
                    ]);
                    $totalPaid += $payment['amount'];
                }

                // Update payment status
                if ($totalPaid >= $totalAmount) {
                    $sale->update(['payment_status' => 'paid']);
                } elseif ($totalPaid > 0) {
                    $sale->update(['payment_status' => 'partial']);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $sale->load(['saleItems.product', 'payments']),
                'message' => 'Sale created successfully.'
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create sale: ' . $e->getMessage()
            ], 400);
        }
    }

    /**
     * Show specific sale
     */
    public function show(Sale $sale)
    {
        $user = Auth::user();

        // Check access
        if (!$user->isMasterAdmin() && $sale->branch->company_id !== $user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $sale->load(['branch.company', 'user', 'posDevice', 'saleItems.product', 'payments'])
        ]);
    }

    /**
     * Update sale (limited fields)
     */
    public function update(Request $request, Sale $sale)
    {
        $validated = $request->validate([
            'notes' => 'nullable|string',
            'payment_status' => 'sometimes|in:pending,partial,paid,refunded'
        ]);

        $user = Auth::user();

        // Check access
        if (!$user->isMasterAdmin() && $sale->branch->company_id !== $user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.'
            ], 403);
        }

        $sale->update($validated);

        return response()->json([
            'success' => true,
            'data' => $sale->load(['saleItems.product', 'payments']),
            'message' => 'Sale updated successfully.'
        ]);
    }

    /**
     * Delete sale (soft delete or mark as cancelled)
     */
    public function destroy(Sale $sale)
    {
        $user = Auth::user();

        // Check access
        if (!$user->isMasterAdmin() && $sale->branch->company_id !== $user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.'
            ], 403);
        }

        // Restore stock for sale items
        foreach ($sale->saleItems as $item) {
            if ($item->product->stock_quantity !== null) {
                $item->product->increment('stock_quantity', $item->quantity);
            }
        }

        $sale->delete();

        return response()->json([
            'success' => true,
            'message' => 'Sale deleted successfully.'
        ]);
    }

    /**
     * POS Sync endpoint - bulk create sales
     */
    public function sync(Request $request)
    {
        $validated = $request->validate([
            'pos_device_id' => 'required|exists:pos_devices,id',
            'sales' => 'required|array|min:1',
            'sales.*.local_id' => 'required|string',
            'sales.*.branch_id' => 'required|exists:branches,id',
            'sales.*.tax_amount' => 'nullable|numeric|min:0',
            'sales.*.discount_amount' => 'nullable|numeric|min:0',
            'sales.*.sale_date' => 'required|date',
            'sales.*.notes' => 'nullable|string',
            'sales.*.items' => 'required|array|min:1',
            'sales.*.items.*.product_id' => 'required|exists:products,id',
            'sales.*.items.*.quantity' => 'required|integer|min:1',
            'sales.*.items.*.unit_price' => 'required|numeric|min:0',
            'sales.*.items.*.discount_amount' => 'nullable|numeric|min:0',
            'sales.*.payments' => 'nullable|array',
            'sales.*.payments.*.method' => 'required|in:cash,card,bank,wallet',
            'sales.*.payments.*.amount' => 'required|numeric|min:0'
        ]);

        $user = Auth::user();
        $posDevice = PosDevice::find($validated['pos_device_id']);

        // Check access
        if (!$user->isMasterAdmin() && $posDevice->branch->company_id !== $user->company_id) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied.'
            ], 403);
        }

        $syncResults = [];
        $successCount = 0;
        $errorCount = 0;

        foreach ($validated['sales'] as $saleData) {
            DB::beginTransaction();

            try {
                // Generate sale number
                $saleNumber = 'SALE-' . date('Ymd', strtotime($saleData['sale_date'])) . '-' . str_pad(Sale::whereDate('created_at', $saleData['sale_date'])->count() + 1, 4, '0', STR_PAD_LEFT);

                // Create sale
                $sale = Sale::create([
                    'sale_number' => $saleNumber,
                    'pos_device_id' => $validated['pos_device_id'],
                    'branch_id' => $saleData['branch_id'],
                    'user_id' => $user->id,
                    'tax_amount' => $saleData['tax_amount'] ?? 0,
                    'discount_amount' => $saleData['discount_amount'] ?? 0,
                    'sale_date' => $saleData['sale_date'],
                    'notes' => $saleData['notes'] ?? null,
                    'payment_status' => 'pending'
                ]);

                $subtotal = 0;

                // Create sale items
                foreach ($saleData['items'] as $item) {
                    $itemTotal = ($item['unit_price'] * $item['quantity']) - ($item['discount_amount'] ?? 0);

                    SaleItem::create([
                        'sale_id' => $sale->id,
                        'product_id' => $item['product_id'],
                        'quantity' => $item['quantity'],
                        'unit_price' => $item['unit_price'],
                        'total_price' => $itemTotal,
                        'discount_amount' => $item['discount_amount'] ?? 0
                    ]);

                    // Update product stock
                    $product = Product::find($item['product_id']);
                    if ($product->stock_quantity !== null) {
                        $product->decrement('stock_quantity', $item['quantity']);
                    }

                    $subtotal += $itemTotal;
                }

                // Update sale totals
                $totalAmount = $subtotal + $sale->tax_amount - $sale->discount_amount;
                $sale->update([
                    'subtotal' => $subtotal,
                    'total_amount' => $totalAmount
                ]);

                // Process payments
                if (!empty($saleData['payments'])) {
                    $totalPaid = 0;
                    foreach ($saleData['payments'] as $payment) {
                        $sale->payments()->create([
                            'method' => $payment['method'],
                            'amount' => $payment['amount']
                        ]);
                        $totalPaid += $payment['amount'];
                    }

                    // Update payment status
                    if ($totalPaid >= $totalAmount) {
                        $sale->update(['payment_status' => 'paid']);
                    } elseif ($totalPaid > 0) {
                        $sale->update(['payment_status' => 'partial']);
                    }
                }

                DB::commit();

                $syncResults[] = [
                    'local_id' => $saleData['local_id'],
                    'server_id' => $sale->id,
                    'status' => 'success'
                ];
                $successCount++;

            } catch (\Exception $e) {
                DB::rollback();
                $syncResults[] = [
                    'local_id' => $saleData['local_id'],
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
                $errorCount++;
            }
        }

        // Log sync activity
        $posDevice->syncLogs()->create([
            'synced_at' => now(),
            'details' => json_encode([
                'total_sales' => count($validated['sales']),
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'results' => $syncResults
            ])
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'total_processed' => count($validated['sales']),
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'results' => $syncResults
            ],
            'message' => "Sync completed. {$successCount} successful, {$errorCount} errors."
        ]);
    }
}
