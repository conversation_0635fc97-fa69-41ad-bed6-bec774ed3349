<html lang="en">
    <!DOCTYPE html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP System Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            overflow-x: hidden;
            line-height: 1.6;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 300px;
            height: 100vh;
            background: linear-gradient(180deg, #1a202c 0%, #2d3748 50%, #1a202c 100%);
            color: white;
            z-index: 1000;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 0 0 40px rgba(0,0,0,0.3);
            border-right: 2px solid rgba(255,255,255,0.1);
            backdrop-filter: blur(20px);
        }

        .sidebar.collapsed {
            width: 85px;
        }

        .sidebar.collapsed .sidebar-header p,
        .sidebar.collapsed .menu-section-title,
        .sidebar.collapsed .menu-item span {
            opacity: 0;
            visibility: hidden;
        }

        .sidebar.collapsed .sidebar-header h2 {
            font-size: 16px;
        }

        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 12px;
        }

        .sidebar.collapsed .user-info {
            text-align: center;
            padding: 15px 10px;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            position: relative;
        }

        .sidebar-header h2 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .sidebar-toggle {
            position: absolute;
            top: 20px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-section {
            margin-bottom: 30px;
        }

        .menu-section-title {
            padding: 0 20px 10px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.7;
            font-weight: 600;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .menu-item span {
            transition: all 0.3s ease;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: #fff;
            transform: translateX(5px);
        }

        .menu-item.active {
            background: rgba(255,255,255,0.15);
            border-left-color: #fff;
        }

        .menu-item i {
            width: 20px;
            margin-right: 10px;
            text-align: center;
        }

        .menu-item.restricted {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .menu-item.restricted:hover {
            background: none;
            border-left-color: transparent;
            transform: none;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.1);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        .logout-btn {
            width: 100%;
            padding: 8px;
            background: rgba(220, 53, 69, 0.8);
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 12px;
        }

        .logout-btn:hover {
            background: rgba(220, 53, 69, 1);
        }

        /* Main Content */
        .main-content {
            margin-left: 300px;
            min-height: 100vh;
            transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        .main-content.expanded {
            margin-left: 85px;
        }

        .top-bar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }

        .breadcrumb {
            font-size: 14px;
            color: #666;
        }

        .content-area {
            padding: 30px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-toggle {
                display: block;
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
            }
        }

        .mobile-toggle {
            display: none;
        }

        /* Header User Section */
        .header-user-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .notification-icon {
            position: relative;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .notification-icon:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #e53e3e;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 11px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
        }

        .user-dropdown {
            position: relative;
        }

        .user-dropdown-toggle {
            display: flex;
            align-items: center;
            gap: 12px;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 8px 16px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .user-dropdown-toggle:hover {
            border-color: #667eea;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.15);
            transform: translateY(-2px);
        }

        .user-avatar-small {
            width: 35px;
            height: 35px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .user-info-text {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .user-name-small {
            font-weight: 600;
            font-size: 14px;
            color: #2d3748;
            line-height: 1.2;
        }

        .user-role-small {
            font-size: 11px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .dropdown-arrow {
            font-size: 12px;
            color: #718096;
            transition: transform 0.3s ease;
        }

        .user-dropdown.active .dropdown-arrow {
            transform: rotate(180deg);
        }

        .user-dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
            min-width: 220px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
            margin-top: 8px;
        }

        .user-dropdown.active .user-dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 20px;
            border-bottom: 1px solid #e2e8f0;
            text-align: center;
        }

        .dropdown-avatar {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            color: white;
            margin: 0 auto 12px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .dropdown-name {
            font-weight: 700;
            font-size: 16px;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .dropdown-role {
            font-size: 12px;
            color: #718096;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-weight: 500;
        }

        .dropdown-menu-items {
            padding: 15px 0;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: #4a5568;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .dropdown-item:hover {
            background: #f7fafc;
            color: #2d3748;
            transform: translateX(5px);
        }

        .dropdown-item i {
            width: 20px;
            margin-right: 12px;
            text-align: center;
            font-size: 16px;
        }

        .dropdown-logout {
            border-top: 1px solid #e2e8f0;
            margin-top: 10px;
            padding-top: 15px;
        }

        .logout-btn-dropdown {
            width: 100%;
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin: 0 15px;
            width: calc(100% - 30px);
        }

        .logout-btn-dropdown:hover {
            background: linear-gradient(135deg, #c53030, #9c2626);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(229, 62, 62, 0.3);
        }

        /* Dashboard Cards */
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #666;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .card-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .card-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }

        .card-link {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }

        .card-link:hover {
            text-decoration: underline;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quick-action-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
            display: block;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: linear-gradient(135deg, #007bff, #0056b3); }
        .btn-success { background: linear-gradient(135deg, #28a745, #1e7e34); }
        .btn-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .btn-info { background: linear-gradient(135deg, #17a2b8, #138496); }

        /* Recent Activity */
        .recent-activity {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .activity-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .activity-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .activity-list {
            padding: 20px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 16px;
            color: #666;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 12px;
            color: #666;
        }

        .activity-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>ERP System</h2>
            <p>Management Dashboard</p>
            <button class="sidebar-toggle" onclick="toggleSidebarCollapse()">
                <span id="toggle-icon">‹</span>
            </button>
        </div>

        <div class="sidebar-menu">
            @if(Auth::user()->isMasterAdmin())
                <!-- Master Admin Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Dashboard</div>
                    <a href="{{ route('dashboard') }}" class="menu-item active">
                        <i>📊</i> <span>Overview</span>
                    </a>
                </div>

                <div class="menu-section">
                    <div class="menu-section-title">Master Management</div>
                    <a href="{{ route('admin.companies.index') }}" class="menu-item">
                        <i>🏢</i> <span>Companies</span>
                    </a>
                    <a href="{{ route('admin.brands.index') }}" class="menu-item">
                        <i>🏷️</i> <span>Brands</span>
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="menu-item">
                        <i>👥</i> <span>Users</span>
                    </a>
                </div>

                <div class="menu-section">
                    <div class="menu-section-title">System</div>
                    <a href="/pos-devices" class="menu-item">
                        <i>🖥️</i> <span>POS Devices</span>
                    </a>
                    <a href="/sales" class="menu-item">
                        <i>💰</i> <span>Sales</span>
                    </a>
                    <a href="/sync-logs" class="menu-item">
                        <i>🔄</i> <span>Sync Logs</span>
                    </a>
                </div>
            @else
                <!-- Company User Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Dashboard</div>
                    <a href="{{ route('dashboard') }}" class="menu-item active">
                        <i>📊</i> <span>Overview</span>
                    </a>
                </div>

                @if(Auth::user()->role === 'admin' || Auth::user()->role === 'manager')
                    <!-- Admin & Manager Menu -->
                    <div class="menu-section">
                        <div class="menu-section-title">Management</div>
                        @if(Auth::user()->canManageBrands())
                            <a href="{{ route('admin.brands.index') }}" class="menu-item">
                                <i>🏷️</i> <span>Brands</span>
                            </a>
                        @else
                            <a href="#" class="menu-item restricted">
                                <i>🏷️</i> <span>Brands (Admin Only)</span>
                            </a>
                        @endif

                        @if(Auth::user()->canManageBranches())
                            <a href="/branches" class="menu-item">
                                <i>🏪</i> <span>Branches</span>
                            </a>
                        @else
                            <a href="#" class="menu-item restricted">
                                <i>🏪</i> <span>Branches (Admin Only)</span>
                            </a>
                        @endif

                        <a href="{{ route('admin.users.index') }}" class="menu-item">
                            <i>👥</i> <span>Users</span>
                        </a>
                    </div>

                    <div class="menu-section">
                        <div class="menu-section-title">Product Management</div>
                        <a href="{{ route('admin.categories.index') }}" class="menu-item">
                            <i>📂</i> <span>Categories</span>
                        </a>
                        <a href="{{ route('admin.divisions.index') }}" class="menu-item">
                            <i>📋</i> <span>Divisions</span>
                        </a>
                        <a href="{{ route('admin.groups.index') }}" class="menu-item">
                            <i>📊</i> <span>Groups</span>
                        </a>
                        <a href="{{ route('admin.products.index') }}" class="menu-item">
                            <i>📦</i> <span>Products</span>
                        </a>
                    </div>

                    <div class="menu-section">
                        <div class="menu-section-title">POS System</div>
                        <a href="{{ route('admin.pos-layouts.index') }}" class="menu-item">
                            <i>🎨</i> <span>POS Layouts</span>
                        </a>
                        <a href="/pos-devices" class="menu-item">
                            <i>🖥️</i> <span>POS Devices</span>
                        </a>
                    </div>
                @endif

                @if(Auth::user()->role === 'user' || Auth::user()->role === 'pos_user')
                    <!-- Employee Menu -->
                    <div class="menu-section">
                        <div class="menu-section-title">Product Catalog</div>
                        <a href="{{ route('admin.categories.index') }}" class="menu-item">
                            <i>📂</i> <span>Categories</span>
                        </a>
                        <a href="{{ route('admin.divisions.index') }}" class="menu-item">
                            <i>�</i> <span>Divisions</span>
                        </a>
                        <a href="{{ route('admin.groups.index') }}" class="menu-item">
                            <i>📊</i> <span>Groups</span>
                        </a>
                        <a href="{{ route('admin.products.index') }}" class="menu-item">
                            <i>📦</i> <span>Products</span>
                        </a>
                    </div>
                @endif

                <div class="menu-section">
                    <div class="menu-section-title">Operations</div>
                    @if(Auth::user()->role === 'pos_user')
                        <a href="/pos" class="menu-item">
                            <i>�</i> <span>POS Terminal</span>
                        </a>
                    @endif
                    <a href="/sales" class="menu-item">
                        <i>💰</i> <span>Sales</span>
                    </a>
                    @if(Auth::user()->role === 'admin' || Auth::user()->role === 'manager')
                        <a href="/reports" class="menu-item">
                            <i>📈</i> <span>Reports</span>
                        </a>
                        <a href="/inventory" class="menu-item">
                            <i>📋</i> <span>Inventory</span>
                        </a>
                    @endif
                </div>
            @endif
        </div>


    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div>
                <button class="mobile-toggle" onclick="toggleSidebar()">☰</button>
                <span class="page-title">
                    @if(Auth::user()->isMasterAdmin())
                        Master Admin Dashboard
                    @else
                        Company Dashboard
                    @endif
                </span>
            </div>
            <div class="header-user-section">
                <!-- Notification Icon -->
                <button class="notification-icon" onclick="toggleNotifications()">
                    🔔
                    <span class="notification-badge">3</span>
                </button>

                <!-- User Dropdown -->
                <div class="user-dropdown" id="userDropdown">
                    <div class="user-dropdown-toggle" onclick="toggleUserDropdown()">
                        <div class="user-avatar-small">
                            {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                        </div>
                        <div class="user-info-text">
                            <div class="user-name-small">{{ Auth::user()->name }}</div>
                            <div class="user-role-small">
                                @if(Auth::user()->isMasterAdmin())
                                    Master Admin
                                @else
                                    {{ ucfirst(Auth::user()->role) }}
                                @endif
                            </div>
                        </div>
                        <div class="dropdown-arrow">▼</div>
                    </div>

                    <div class="user-dropdown-menu">
                        <div class="dropdown-header">
                            <div class="dropdown-avatar">
                                {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                            </div>
                            <div class="dropdown-name">{{ Auth::user()->name }}</div>
                            <div class="dropdown-role">
                                @if(Auth::user()->isMasterAdmin())
                                    Master Administrator
                                @else
                                    {{ ucfirst(Auth::user()->role) }} - {{ Auth::user()->company->name ?? 'No Company' }}
                                @endif
                            </div>
                        </div>

                        <div class="dropdown-menu-items">
                            <a href="#" class="dropdown-item">
                                <i>👤</i> Profile Settings
                            </a>
                            <a href="#" class="dropdown-item">
                                <i>⚙️</i> Account Settings
                            </a>
                            <a href="#" class="dropdown-item">
                                <i>🔔</i> Notifications
                            </a>
                            <a href="#" class="dropdown-item">
                                <i>❓</i> Help & Support
                            </a>
                        </div>

                        <div class="dropdown-logout">
                            <form action="{{ route('logout') }}" method="POST" style="margin: 0;">
                                @csrf
                                <button type="submit" class="logout-btn-dropdown">
                                    🚪 Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            @if(Auth::user()->isMasterAdmin())
                <!-- Master Admin Dashboard Content -->
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Companies</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">🏢</div>
                        </div>
                        <div class="card-value">{{ \App\Models\Company::count() }}</div>
                        <div class="card-description">Total Licensed Companies</div>
                        <a href="{{ route('admin.companies.index') }}" class="card-link">Manage Companies →</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Brands</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #28a745, #1e7e34);">🏷️</div>
                        </div>
                        <div class="card-value">{{ \App\Models\Brand::count() }}</div>
                        <div class="card-description">Total Brands</div>
                        <a href="{{ route('admin.brands.index') }}" class="card-link">Manage Brands →</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Users</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">👥</div>
                        </div>
                        <div class="card-value">{{ \App\Models\User::where('id', '!=', 1)->count() }}</div>
                        <div class="card-description">Total System Users</div>
                        <a href="{{ route('admin.users.index') }}" class="card-link">Manage Users →</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">POS Devices</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">🖥️</div>
                        </div>
                        <div class="card-value">{{ \App\Models\PosDevice::count() }}</div>
                        <div class="card-description">Active POS Devices</div>
                        <a href="#" class="card-link">Manage Devices →</a>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <a href="{{ route('admin.companies.create') }}" class="quick-action-btn btn-primary">
                        + Add New Company
                    </a>
                    <a href="{{ route('admin.brands.create') }}" class="quick-action-btn btn-success">
                        + Add New Brand
                    </a>
                    <a href="{{ route('admin.users.create') }}" class="quick-action-btn btn-warning">
                        + Add New User
                    </a>
                </div>

                <!-- Recent Activity -->
                <div class="recent-activity">
                    <div class="activity-header">
                        <h3>Recent Activity</h3>
                    </div>
                    <div class="activity-list">
                        @php
                            $recentCompanies = \App\Models\Company::latest()->take(5)->get();
                        @endphp
                        @if($recentCompanies->count() > 0)
                            @foreach($recentCompanies as $company)
                                <div class="activity-item">
                                    <div class="activity-icon">🏢</div>
                                    <div class="activity-content">
                                        <div class="activity-title">{{ $company->name }}</div>
                                        <div class="activity-time">Added {{ $company->created_at->diffForHumans() }}</div>
                                    </div>
                                    <div class="activity-status {{ $company->license_expiry > now() ? 'status-active' : 'status-expired' }}">
                                        {{ $company->license_expiry > now() ? 'Active' : 'Expired' }}
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="activity-item">
                                <div class="activity-icon">📝</div>
                                <div class="activity-content">
                                    <div class="activity-title">No recent activity</div>
                                    <div class="activity-time">Start by adding your first company</div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            @else
                <!-- Company User Dashboard Content -->
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Your Company</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">🏢</div>
                        </div>
                        <div class="card-value">{{ Auth::user()->company->name }}</div>
                        <div class="card-description">
                            License: {{ Auth::user()->company->license_expiry > now() ? 'Active' : 'Expired' }}
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Brands</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #28a745, #1e7e34);">🏷️</div>
                        </div>
                        <div class="card-value">{{ Auth::user()->company->brands->count() }}</div>
                        <div class="card-description">Company Brands</div>
                        @if(Auth::user()->canManageBrands())
                            <a href="{{ route('admin.brands.index') }}" class="card-link">Manage Brands →</a>
                        @endif
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Branches</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">🏪</div>
                        </div>
                        <div class="card-value">{{ Auth::user()->company->branches->count() }}</div>
                        <div class="card-description">Store Locations</div>
                        @if(Auth::user()->canManageBranches())
                            <a href="/branches" class="card-link">Manage Branches →</a>
                        @endif
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">POS Devices</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">🖥️</div>
                        </div>
                        <div class="card-value">{{ Auth::user()->company->branches->sum(function($branch) { return $branch->posDevices->count(); }) }}</div>
                        <div class="card-description">Active Devices</div>
                        <a href="/pos-devices" class="card-link">View Devices →</a>
                    </div>
                </div>

                <!-- Company Quick Actions -->
                <div class="quick-actions">
                    @if(Auth::user()->canManageBrands())
                        <a href="{{ route('admin.brands.create', ['company_id' => Auth::user()->company_id]) }}" class="quick-action-btn btn-success">
                            + Add New Brand
                        </a>
                    @endif
                    <a href="{{ route('admin.users.create', ['company_id' => Auth::user()->company_id]) }}" class="quick-action-btn btn-warning">
                        + Add New User
                    </a>
                    <a href="/products" class="quick-action-btn btn-info">
                        📦 Manage Products
                    </a>
                </div>
            @endif
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('active');
        }

        function toggleSidebarCollapse() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('#main-content');
            const toggleIcon = document.querySelector('#toggle-icon');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // Update toggle icon
            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.textContent = '›';
            } else {
                toggleIcon.textContent = '‹';
            }
        }

        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('active');
        }

        function toggleNotifications() {
            // Add notification functionality here
            alert('Notifications feature coming soon!');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggle = document.querySelector('.mobile-toggle');
            const userDropdown = document.getElementById('userDropdown');
            const userToggle = document.querySelector('.user-dropdown-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggle.contains(event.target)) {
                sidebar.classList.remove('active');
            }

            // Close user dropdown when clicking outside
            if (!userDropdown.contains(event.target)) {
                userDropdown.classList.remove('active');
            }
        });

        // Add tooltips for collapsed sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.menu-item');

            menuItems.forEach(item => {
                const span = item.querySelector('span');
                if (span) {
                    item.setAttribute('title', span.textContent);
                }
            });
        });
    </script>



</body>
</html>
