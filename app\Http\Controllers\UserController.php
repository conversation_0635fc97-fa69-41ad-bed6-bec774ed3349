<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;

class UserController extends Controller {
    public function index() { return User::all(); }
    public function store(Request $r) {
        $validated = $r->validate([
            'name' => 'required',
            'email' => 'required|email|unique:users',
            'password' => 'required|min:6',
            'role' => 'required',
            'company_id' => 'required|exists:companies,id',
            'branch_id' => 'nullable|exists:branches,id'
        ]);
        $validated['password'] = bcrypt($validated['password']);
        return User::create($validated);
    }
    public function show(User $user) { return $user; }
    public function update(Request $r, User $user) { $user->update($r->except(['password'])); return $user; }
    public function destroy(User $user) { $user->delete(); return response()->noContent(); }
}