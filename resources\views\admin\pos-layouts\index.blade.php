@extends('layouts.admin')

@section('title', 'POS Layouts Management')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">POS Layouts Management</h3>
                    <a href="{{ route('admin.pos-layouts.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add New Layout
                    </a>
                </div>

                <div class="card-body">
                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-control" id="brandFilter">
                                <option value="">All Brands</option>
                                @foreach($brands as $brand)
                                    <option value="{{ $brand->id }}">{{ $brand->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-control" id="statusFilter">
                                <option value="">All Status</option>
                                <option value="1">Active</option>
                                <option value="0">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <input type="text" class="form-control" id="searchInput" placeholder="Search layouts...">
                        </div>
                    </div>

                    @if($layouts->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped" id="layoutsTable">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Brand</th>
                                        <th>Description</th>
                                        <th>Default</th>
                                        <th>Groups</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($layouts as $layout)
                                        <tr data-brand-id="{{ $layout->brand_id }}">
                                            <td>
                                                <strong>{{ $layout->name }}</strong>
                                                @if($layout->is_default)
                                                    <span class="badge badge-success ml-2">Default</span>
                                                @endif
                                            </td>
                                            <td>{{ $layout->brand->name }}</td>
                                            <td>{{ $layout->description ?? 'No description' }}</td>
                                            <td>
                                                @if($layout->is_default)
                                                    <span class="badge badge-success">Yes</span>
                                                @else
                                                    <span class="badge badge-secondary">No</span>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge badge-info">{{ $layout->groups_count ?? 0 }}</span>
                                            </td>
                                            <td>{{ $layout->created_at->format('M d, Y') }}</td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('admin.pos-layouts.show', $layout) }}" class="btn btn-sm btn-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('admin.pos-layouts.edit', $layout) }}" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    @if(!$layout->is_default)
                                                        <form action="{{ route('admin.pos-layouts.destroy', $layout) }}" method="POST" class="d-inline">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" class="btn btn-sm btn-danger" 
                                                                    onclick="return confirm('Are you sure you want to delete this layout?')">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $layouts->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-th-large fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No POS Layouts Found</h5>
                            <p class="text-muted">Start by creating your first POS layout.</p>
                            <a href="{{ route('admin.pos-layouts.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create First Layout
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#layoutsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });

    // Brand filter
    $('#brandFilter').on('change', function() {
        var brandId = $(this).val();
        $('#layoutsTable tbody tr').each(function() {
            if (brandId === '' || $(this).data('brand-id') == brandId) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Status filter
    $('#statusFilter').on('change', function() {
        var status = $(this).val();
        $('#layoutsTable tbody tr').each(function() {
            var isDefault = $(this).find('.badge-success').length > 0;
            if (status === '' || (status == '1' && isDefault) || (status == '0' && !isDefault)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });
});
</script>
@endpush
@endsection
