<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\POSDeviceController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DivisionController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\SyncLogController;
use App\Http\Controllers\POSLayoutController;
use App\Http\Controllers\POSLimitController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\NotificationController;

Route::apiResource('companies', CompanyController::class);
Route::apiResource('brands', BrandController::class);
Route::apiResource('branches', BranchController::class);
Route::apiResource('pos-devices', POSDeviceController::class);
Route::apiResource('users', UserController::class);
Route::apiResource('categories', CategoryController::class);   
Route::apiResource('divisions', DivisionController::class);
Route::apiResource('groups', GroupController::class);
Route::apiResource('products', ProductController::class);
Route::apiResource('sales', SaleController::class);
Route::apiResource('sync-logs', SyncLogController::class);
Route::apiResource('pos-layouts', POSLayoutController::class);
Route::apiResource('pos-limits', POSLimitController::class);
Route::apiResource('purchase-orders', PurchaseOrderController::class);
Route::apiResource('payments', PaymentController::class);
Route::apiResource('notifications', NotificationController::class);
