<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Company;
use App\Models\Brand;
use App\Models\User;

class AdminController extends Controller
{
    // Middleware is applied in routes/web.php

    // Company Management
    public function companiesIndex()
    {
        $companies = Company::with(['brands', 'branches.posDevices', 'users'])
                           ->paginate(15);

        return view('admin.companies.index', compact('companies'));
    }

    public function companiesCreate()
    {
        return view('admin.companies.create');
    }

    public function companiesStore(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'license_key' => 'required|string|max:255|unique:companies,license_key',
            'license_expiry' => 'required|date|after:today',
            'pos_limit' => 'required|integer|min:1|max:100',
            'status' => 'nullable|in:active,inactive,suspended',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'notes' => 'nullable|string'
        ]);

        $validated['status'] = $validated['status'] ?? 'active';

        $company = Company::create($validated);

        return redirect()->route('admin.companies.show', $company)
                        ->with('success', 'Company created successfully!');
    }

    public function companiesShow(Company $company)
    {
        $company->load(['brands.categories', 'branches.posDevices', 'users.branch']);

        return view('admin.companies.show', compact('company'));
    }

    public function companiesEdit(Company $company)
    {
        return view('admin.companies.edit', compact('company'));
    }

    public function companiesUpdate(Request $request, Company $company)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'license_key' => 'required|string|max:255|unique:companies,license_key,' . $company->id,
            'license_expiry' => 'required|date',
            'pos_limit' => 'required|integer|min:1|max:100',
            'status' => 'nullable|in:active,inactive,suspended',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'notes' => 'nullable|string'
        ]);

        $company->update($validated);

        return redirect()->route('admin.companies.show', $company)
                        ->with('success', 'Company updated successfully!');
    }

    public function companiesDestroy(Company $company)
    {
        // Check if company has any data
        if ($company->brands()->count() > 0 || $company->users()->count() > 0) {
            return redirect()->route('admin.companies.index')
                           ->with('error', 'Cannot delete company with existing brands or users.');
        }

        $companyName = $company->name;
        $company->delete();

        return redirect()->route('admin.companies.index')
                        ->with('success', "Company '{$companyName}' deleted successfully!");
    }

    // Brand Management
    public function brandsIndex(Request $request)
    {
        $query = Brand::with(['company', 'categories', 'branches']);

        if ($request->has('company_id')) {
            $query->where('company_id', $request->company_id);
        }

        $brands = $query->paginate(15);
        $companies = Company::all();

        return view('admin.brands.index', compact('brands', 'companies'));
    }

    public function brandsCreate(Request $request)
    {
        $companies = Company::all();
        $selectedCompany = $request->get('company_id');

        return view('admin.brands.create', compact('companies', 'selectedCompany'));
    }

    public function brandsStore(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'company_id' => 'required|exists:companies,id',
            'description' => 'nullable|string'
        ]);

        $brand = Brand::create($validated);

        return redirect()->route('admin.brands.show', $brand)
                        ->with('success', 'Brand created successfully!');
    }

    public function brandsShow(Brand $brand)
    {
        $brand->load(['company', 'categories.divisions.groups.products', 'branches.posDevices']);

        return view('admin.brands.show', compact('brand'));
    }

    public function brandsEdit(Brand $brand)
    {
        $companies = Company::all();

        return view('admin.brands.edit', compact('brand', 'companies'));
    }

    public function brandsUpdate(Request $request, Brand $brand)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'company_id' => 'required|exists:companies,id',
            'description' => 'nullable|string'
        ]);

        $brand->update($validated);

        return redirect()->route('admin.brands.show', $brand)
                        ->with('success', 'Brand updated successfully!');
    }

    public function brandsDestroy(Brand $brand)
    {
        // Check if brand has any data
        if ($brand->categories()->count() > 0 || $brand->branches()->count() > 0) {
            return redirect()->route('admin.brands.index')
                           ->with('error', 'Cannot delete brand with existing categories or branches.');
        }

        $brandName = $brand->name;
        $brand->delete();

        return redirect()->route('admin.brands.index')
                        ->with('success', "Brand '{$brandName}' deleted successfully!");
    }

    // User Management
    public function usersIndex(Request $request)
    {
        $query = User::with(['company', 'branch'])
                    ->where('id', '!=', 1); // Exclude master admin

        if ($request->has('company_id')) {
            $query->where('company_id', $request->company_id);
        }

        if ($request->has('role')) {
            $query->where('role', $request->role);
        }

        $users = $query->paginate(15);
        $companies = Company::all();

        return view('admin.users.index', compact('users', 'companies'));
    }

    public function usersCreate(Request $request)
    {
        $companies = Company::with('branches')->get();
        $selectedCompany = $request->get('company_id');

        return view('admin.users.create', compact('companies', 'selectedCompany'));
    }

    public function usersStore(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6|confirmed',
            'company_id' => 'required|exists:companies,id',
            'branch_id' => 'nullable|exists:branches,id',
            'role' => 'required|in:admin,manager,user,pos_user'
        ]);

        $validated['password'] = bcrypt($validated['password']);

        $user = User::create($validated);

        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User created successfully!');
    }

    public function usersShow(User $user)
    {
        $user->load(['company', 'branch.brand']);

        return view('admin.users.show', compact('user'));
    }

    public function usersEdit(User $user)
    {
        $companies = Company::with('branches')->get();

        return view('admin.users.edit', compact('user', 'companies'));
    }

    public function usersUpdate(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:6|confirmed',
            'company_id' => 'required|exists:companies,id',
            'branch_id' => 'nullable|exists:branches,id',
            'role' => 'required|in:admin,manager,user,pos_user'
        ]);

        if (!empty($validated['password'])) {
            $validated['password'] = bcrypt($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);

        return redirect()->route('admin.users.show', $user)
                        ->with('success', 'User updated successfully!');
    }

    public function usersDestroy(User $user)
    {
        // Prevent deleting master admin
        if ($user->id === 1) {
            return redirect()->route('admin.users.index')
                           ->with('error', 'Cannot delete master admin user.');
        }

        $userName = $user->name;
        $user->delete();

        return redirect()->route('admin.users.index')
                        ->with('success', "User '{$userName}' deleted successfully!");
    }
}
